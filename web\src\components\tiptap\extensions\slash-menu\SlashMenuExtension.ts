import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'
import { Decoration, DecorationSet } from '@tiptap/pm/view'
import Suggestion from '@tiptap/suggestion'

import { SlashMenuView } from './SlashMenuView'
import type { SlashMenuItem } from './types'

export interface SlashMenuOptions {
  items: SlashMenuItem[]
  dictionary: {
    lineEmpty: string
    lineSlash: string
    queryEmpty: string
  }
}

export const SlashMenuExtension = Extension.create<SlashMenuOptions>({
  name: 'slashMenu',

  addOptions() {
    return {
      items: [],
      dictionary: {
        lineEmpty: "输入 '/' 插入块...",
        lineSlash: '继续输入以过滤...',
        queryEmpty: '未找到结果',
      },
    }
  },

  addProseMirrorPlugins() {
    return [
      Suggestion({
        editor: this.editor,
        pluginKey: new PluginKey(`${this.name}-suggestion`),
        char: '/',
        items: ({ query }) => {
          const filtered: SlashMenuItem[] = []

          for (const item of this.options.items) {
            if (item === '|') {
              filtered.push(item)
              continue
            }

            if (query !== '') {
              const q = query.toLowerCase()
              if (
                !item.name.toLowerCase().includes(q) &&
                !item.keywords.toLowerCase().includes(q)
              ) {
                continue
              }
            }

            filtered.push({
              ...item,
              action: (editor) => {
                // 清除搜索文本
                const { state, dispatch } = editor.view
                const from = state.selection.$from
                const tr = state.tr.deleteRange(from.start(), from.pos)
                dispatch(tr)

                // 执行命令
                item.action(editor)

                // 聚焦编辑器
                editor.view.focus()
              },
            })
          }

          // 过滤连续的分隔符
          const items: SlashMenuItem[] = []
          for (let i = 0; i < filtered.length; i++) {
            const item = filtered[i]
            if (item === '|') {
              if (i === 0 || i === filtered.length - 1) {
                continue
              }
              if (filtered[i + 1] === '|') {
                continue
              }
              if (items.length === 0) {
                continue
              }
              if (items[items.length - 1] === '|') {
                continue
              }
            }
            items.push(item)
          }

          return items
        },
        render: SlashMenuView.create({
          editor: this.editor,
          dictionary: {
            empty: this.options.dictionary.queryEmpty,
          },
        }),
      }),

      // 占位符插件
      new Plugin({
        key: new PluginKey(`${this.name}-placeholder`),
        props: {
          decorations: (state) => {
            const { $from } = state.selection
            const parent = $from.parent

            if (parent.type.name !== 'paragraph') {
              return null
            }

            const decorations: Decoration[] = []
            const isEmpty = parent.content.size === 0
            const isSlash = parent.textContent === '/'
            const isTopLevel = $from.depth === 1

            if (isTopLevel) {
              if (isEmpty) {
                decorations.push(
                  Decoration.node($from.start() - 1, $from.end() + 1, {
                    class: 'slash-menu-placeholder',
                    'data-placeholder': this.options.dictionary.lineEmpty,
                  }),
                )
              }

              if (isSlash) {
                decorations.push(
                  Decoration.node($from.start() - 1, $from.end() + 1, {
                    class: 'slash-menu-placeholder',
                    'data-placeholder': ` ${this.options.dictionary.lineSlash}`,
                  }),
                )
              }

              return DecorationSet.create(state.doc, decorations)
            }

            return null
          },
        },
      }),
    ]
  },
})
