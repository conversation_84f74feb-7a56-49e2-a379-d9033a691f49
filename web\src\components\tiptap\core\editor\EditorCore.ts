import Placeholder from '@tiptap/extension-placeholder'
import { Editor, Extension, type JSONContent, type AnyExtension } from '@tiptap/vue-3'
import { ref, watch } from 'vue'

import { CharacterCountExtension } from '@/components/tiptap/extensions/character-count/CharacterCountExtension'
import { FormatPainterExtension } from '@/components/tiptap/extensions/format-painter/FormatPainterExtension'
import { createImageExtension } from '@/components/tiptap/extensions/image/ImageExtension'
import type { EditorWithFormatPainter } from '@/types/tiptap.types'
import logger from '@/utils/log'
import message from '@/utils/message'
import tiptap from '@/utils/tiptap'

/**
 * 编辑器核心逻辑封装
 * 创建、配置和管理编辑器实例
 */
interface EditorProps {
  placeholder: string
  characterLimit: number
  useThumbnail?: boolean
  extensions: string[]
  allExtensions: boolean
  modelValue: Record<string, unknown>
  editable: boolean
  editorProps: Record<string, unknown>
}

type EmitFn = (event: 'update:modelValue', content: JSONContent | null) => void

export const createEditor = (props: EditorProps, emit: EmitFn) => {
  // 编辑器实例
  const editor = ref<EditorWithFormatPainter>()

  // Placeholder扩展配置
  const placeholder = Placeholder.configure({
    placeholder: props.placeholder,
  })

  // 字符计数扩展配置
  const characterCount = CharacterCountExtension.configure({
    limit: props.characterLimit,
  })

  // 格式刷扩展配置
  const formatPainter = FormatPainterExtension.configure({
    enabled: true,
  })

  /**
   * 初始化编辑器
   * 加载扩展并设置初始内容
   */
  const initEditor = () => {
    tiptap.extensionMap.set('placeholder', placeholder)
    tiptap.extensionMap.set('characterCount', characterCount)
    tiptap.extensionMap.set('formatPainter', formatPainter)

    if (props.useThumbnail !== undefined) {
      tiptap.extensionMap.set('image', createImageExtension(props.useThumbnail))
    }

    const extensions: Array<Extension | AnyExtension> = []
    let extensionsSet = new Set<string>(['document', 'paragraph', 'text', ...props.extensions])

    if (props.allExtensions) {
      extensions.push(...tiptap.extensionMap.values())
      extensionsSet = new Set([...tiptap.extensionMap.keys()])
    } else {
      extensionsSet.forEach((extensionName) => {
        const extension = tiptap.extensionMap.get(extensionName)
        if (extension) {
          extensions.push(extension)
        } else {
          message.warning(`Unknown extension: ${extensionName}`)
        }
      })
    }

    logger.debug('extensions: ', extensions)

    editor.value = new Editor({
      extensions,
      content: Object.keys(props.modelValue).length ? props.modelValue : '',
      editable: props.editable,
      editorProps: props.editorProps,
    }) as EditorWithFormatPainter

    // 设置更新内容的事件处理
    editor.value?.on('update', () => {
      const content = editor.value?.getJSON() || null
      logger.debug('update content: ', content)
      emit('update:modelValue', content)
    })

    return extensionsSet
  }

  /**
   * 监听编辑状态变化
   */
  const watchEditable = () => {
    watch(
      () => props.editable,
      (newEditable) => {
        if (editor.value) {
          // 更新编辑器的editable状态
          editor.value.setEditable(newEditable)

          // 在状态变化后更新任务列表样式
          requestAnimationFrame(() => {
            try {
              // 获取所有任务列表复选框包装器
              const taskCheckboxes = document.querySelectorAll('.cst-task-checkbox-wrapper')
              taskCheckboxes.forEach((checkbox) => {
                // 根据编辑状态更新样式
                if (!newEditable) {
                  checkbox.classList.add('readonly-checkbox')
                  checkbox.setAttribute('aria-disabled', 'true')
                  checkbox.setAttribute('title', '只读模式下不可更改')
                  ;(checkbox as HTMLElement).style.cursor = 'not-allowed'
                  ;(checkbox as HTMLElement).style.transition = 'none'
                  ;(checkbox as HTMLElement).style.transform = 'none'

                  // 获取父级标签元素并移除悬浮动画
                  const label = checkbox.closest('label')
                  if (label) {
                    ;(label as HTMLElement).style.transition = 'none'
                    ;(label as HTMLElement).style.transform = 'none'
                  }
                } else {
                  checkbox.classList.remove('readonly-checkbox')
                  checkbox.removeAttribute('aria-disabled')
                  checkbox.removeAttribute('title')
                  ;(checkbox as HTMLElement).style.cursor = 'pointer'
                  ;(checkbox as HTMLElement).style.transition = 'background-color 0.2s ease'
                  ;(checkbox as HTMLElement).style.transform = ''

                  // 恢复父级标签元素的悬浮动画
                  const label = checkbox.closest('label')
                  if (label) {
                    ;(label as HTMLElement).style.transition = 'transform 0.15s ease'
                    ;(label as HTMLElement).style.transform = ''
                    ;(label as HTMLElement).style.pointerEvents = ''
                  }
                }
              })
            } catch (error) {
              logger.error('Error updating task checkboxes in readonly mode:', error)
            }
          })
        }
      },
    )
  }

  /**
   * 清除编辑器内容
   */
  const clearContent = () => {
    editor.value?.commands.clearContent()
  }

  /**
   * 设置编辑器内容
   * @param content 要设置的内容
   */
  const setContent = (content: JSONContent) => {
    editor.value?.commands.setContent(content)
  }

  /**
   * 获取Markdown格式的内容
   */
  const getMarkdown = () => {
    return editor.value?.storage.markdown?.getMarkdown()
  }

  /**
   * 清理函数，在组件卸载时调用
   */
  const cleanupEditor = () => {
    setTimeout(() => {
      editor.value?.destroy()
    }, 1000)
  }

  return {
    editor,
    initEditor,
    watchEditable,
    clearContent,
    setContent,
    getMarkdown,
    cleanupEditor,
  }
}

// 为了保持向后兼容，提供原有的 useEditor 函数
export const useEditor = createEditor
