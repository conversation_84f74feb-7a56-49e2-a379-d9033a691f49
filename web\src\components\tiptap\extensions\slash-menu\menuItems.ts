import type { Editor } from '@tiptap/core'

import type { SlashMenuItem } from './types'

// SVG 图标定义
const icons = {
  heading1: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 12h8m-8 6V6m8 12V6m5 6l3-2v8"/></svg>`,
  heading2: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 12h8m-8 6V6m8 12V6m9 12h-4c0-4 4-3 4-6c0-1.5-2-2.5-4-1"/></svg>`,
  heading3: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 12h8m-8 6V6m8 12V6m5.5 4.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2m-2 3.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2"/></svg>`,
  bulletList: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12h.01M3 18h.01M3 6h.01M8 12h13M8 18h13M8 6h13"/></svg>`,
  orderedList: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M10 12h11m-11 6h11M10 6h11M4 10h2M4 6h1v4m1 8H4c0-1 2-2 2-3s-1-1.5-2-1"/></svg>`,
  taskList: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 17l2 2l4-4M3 7l2 2l4-4m4 1h8m-8 6h8m-8 6h8"/></svg>`,
  blockquote: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1a6 6 0 0 0 6-6V5a2 2 0 0 0-2-2zM5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1a6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z"/></svg>`,
  codeBlock: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m18 16l4-4l-4-4M6 8l-4 4l4 4m8.5-12l-5 16"/></svg>`,
  image: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>`,
  horizontalRule: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 12h18M8 8l4-4l4 4m0 8l-4 4l-4-4"/></svg>`,
}

// 创建默认的斜杠菜单项
export const createDefaultSlashMenuItems = (): SlashMenuItem[] => [
  {
    id: 'heading1',
    name: '标题 1',
    icon: icons.heading1,
    keywords: 'h1 标题 heading',
    shortcut: 'Ctrl+Alt+1',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  {
    id: 'heading2',
    name: '标题 2',
    icon: icons.heading2,
    keywords: 'h2 标题 heading',
    shortcut: 'Ctrl+Alt+2',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  {
    id: 'heading3',
    name: '标题 3',
    icon: icons.heading3,
    keywords: 'h3 标题 heading',
    shortcut: 'Ctrl+Alt+3',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 3 }).run(),
  },
  '|',
  {
    id: 'bulletList',
    name: '无序列表',
    icon: icons.bulletList,
    keywords: 'ul 列表 list bullet',
    action: (editor: Editor) => editor.chain().focus().toggleBulletList().run(),
  },
  {
    id: 'orderedList',
    name: '有序列表',
    icon: icons.orderedList,
    keywords: 'ol 列表 list ordered number',
    action: (editor: Editor) => editor.chain().focus().toggleOrderedList().run(),
  },
  {
    id: 'taskList',
    name: '任务列表',
    icon: icons.taskList,
    keywords: 'todo 任务 task checklist',
    action: (editor: Editor) => editor.chain().focus().toggleTaskList().run(),
  },
  '|',
  {
    id: 'blockquote',
    name: '引用',
    icon: icons.blockquote,
    keywords: 'quote 引用 blockquote',
    action: (editor: Editor) => editor.chain().focus().toggleBlockquote().run(),
  },
  {
    id: 'codeBlock',
    name: '代码块',
    icon: icons.codeBlock,
    keywords: 'code 代码 codeblock',
    shortcut: 'Ctrl+Alt+C',
    action: (editor: Editor) => editor.chain().focus().toggleCodeBlock().run(),
  },
  '|',
  {
    id: 'image',
    name: '图片',
    icon: icons.image,
    keywords: 'image 图片 img picture',
    action: (editor: Editor) => {
      // 简单的图片插入，使用占位符
      const src = prompt('请输入图片URL:')
      if (src) {
        editor.chain().focus().setImage({ src }).run()
      }
    },
  },
  {
    id: 'horizontalRule',
    name: '分割线',
    icon: icons.horizontalRule,
    keywords: 'hr 分割线 divider line',
    action: (editor: Editor) => editor.chain().focus().setHorizontalRule().run(),
  },
]
