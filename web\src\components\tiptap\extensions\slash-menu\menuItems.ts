import type { Editor } from '@tiptap/core'

import type { SlashMenuItem } from './types'

// 图片上传触发器类型
type ImageUploadTrigger = () => void

// 模态框触发器类型
type ModalTrigger = (title: string, callback: () => void, needInput?: boolean) => void

// 与工具栏一致的 SVG 图标定义
// 这些 SVG 路径来自 @vicons 包中对应的图标组件
const icons = {
  // 标题图标 (来自 @vicons/fluent)
  heading1: `<svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor"><path d="M12.5 4v12h-1.25V10.75H6V16H4.75V4H6v5.5h5.25V4h1.25Zm4.5 8v4h-1V12h-1v-1h3v1h-1Z"/></svg>`,
  heading2: `<svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor"><path d="M12.5 4v12h-1.25V10.75H6V16H4.75V4H6v5.5h5.25V4h1.25Zm4.5 8.5c0-.83.67-1.5 1.5-1.5s1.5.67 1.5 1.5-.67 1.5-1.5 1.5-1.5-.67-1.5-1.5Zm0 3.5v-1h3v1h-3Z"/></svg>`,
  heading3: `<svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor"><path d="M12.5 4v12h-1.25V10.75H6V16H4.75V4H6v5.5h5.25V4h1.25Zm4.5 8c0-.55.45-1 1-1s1 .45 1 1-.45 1-1 1-1-.45-1-1Zm0 4c0-.55.45-1 1-1s1 .45 1 1-.45 1-1 1-1-.45-1-1Z"/></svg>`,

  // 文本格式图标
  bold: `<svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor"><path d="M6 4.75c0-.41.34-.75.75-.75h3.84c1.68 0 3.16.81 3.16 2.5 0 .8-.37 1.45-.93 1.86.93.48 1.43 1.25 1.43 2.39 0 1.83-1.48 2.5-3.16 2.5H6.75c-.41 0-.75-.34-.75-.75V4.75ZM7.5 5.5v3h3.09c.95 0 1.66-.48 1.66-1.25S11.54 5.5 10.59 5.5H7.5Zm0 4.25v3.5h3.34c.95 0 1.66-.48 1.66-1.25s-.71-1.25-1.66-1.25H7.5Z"/></svg>`,
  italic: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="19" y1="4" x2="10" y2="4"/><line x1="14" y1="20" x2="5" y2="20"/><line x1="15" y1="4" x2="9" y2="20"/></svg>`,
  strike: `<svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor"><path d="M7.5 6.5c0-.83.67-1.5 1.5-1.5h2c.83 0 1.5.67 1.5 1.5S11.83 8 11 8H9c-.83 0-1.5-.67-1.5-1.5ZM10 11h1c.83 0 1.5.67 1.5 1.5s-.67 1.5-1.5 1.5H9c-.83 0-1.5-.67-1.5-1.5S8.17 11 9 11h1Zm-6 0h12v1H4v-1Z"/></svg>`,
  underline: `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3h-2.25v7a3.75 3.75 0 0 1-3.75 3.75A3.75 3.75 0 0 1 8.25 10V3H6ZM4 21h16v1H4v-1Z"/></svg>`,
  code: `<svg width="16" height="16" viewBox="0 0 512 512" fill="currentColor"><path d="M160 368L32 256l128-112 32 36-96 76 96 76-32 36zm192 0l-32-36 96-76-96-76 32-36 128 112-128 112z"/></svg>`,

  // 列表图标
  bulletList: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2 4a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm0 5a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm0 5a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm3-11h9v1H5V3Zm0 5h9v1H5V8Zm0 5h9v1H5v-1Z"/></svg>`,
  orderedList: `<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M1 3.5h1v3H1v-.5h.5v-.5H1V3.5Zm0 5h1v3H1v-.5h.5v-.5H1V8.5Zm0 5h1v3H1v-.5h.5v-.5H1v-2ZM5 3h9v1H5V3Zm0 5h9v1H5V8Zm0 5h9v1H5v-1Z"/></svg>`,
  taskList: `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 17l2 2 4-4m-6-5l2 2 4-4M3 7l2 2 4-4m4 1h8m-8 6h8m-8 6h8"/></svg>`,

  // 块级元素图标
  blockquote: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2zM5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z"/></svg>`,
  codeBlock: `<svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor"><path d="M4.5 6.5L1 10l3.5 3.5L5.91 12.09 3.83 10l2.08-2.09L4.5 6.5Zm11 0L14.09 7.91 16.17 10l-2.08 2.09L15.5 13.5 19 10l-3.5-3.5ZM9.5 4l-1 12h1l1-12h-1Z"/></svg>`,

  // 媒体图标
  image: `<svg width="16" height="16" viewBox="0 0 28 28" fill="currentColor"><path d="M21 6.25A3.25 3.25 0 0 0 17.75 3H6.25A3.25 3.25 0 0 0 3 6.25v11.5A3.25 3.25 0 0 0 6.25 21h11.5A3.25 3.25 0 0 0 21 17.75V6.25ZM6.25 4.5h11.5c.97 0 1.75.78 1.75 1.75v7.19l-3.22-3.22a1.75 1.75 0 0 0-2.48 0l-3.19 3.19-1.81-1.81a1.75 1.75 0 0 0-2.48 0L4.5 13.42V6.25c0-.97.78-1.75 1.75-1.75ZM4.5 15.48l2.78-2.78c.1-.1.26-.1.36 0l2.28 2.28-1.64 1.64c-.1.1-.1.26 0 .36l3.19 3.19c.1.1.26.1.36 0l3.19-3.19c.1-.1.26-.1.36 0l2.12 2.12v.65c0 .97-.78 1.75-1.75 1.75H6.25c-.97 0-1.75-.78-1.75-1.75v-2.27Z"/></svg>`,
  link: `<svg width="16" height="16" viewBox="0 0 1024 1024" fill="currentColor"><path d="M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"/></svg>`,
  bilibili: `<svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M3 6v12h18V6H3zm2 2h14v8H5V8zm2 2v4h2v-4H7zm4 0v4h2v-4h-2zm4 0v4h2v-4h-2z"/></svg>`,

  // 其他图标
  horizontalRule: `<svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor"><path d="M2 10h16v1H2v-1Z"/></svg>`,
}

// 创建默认的斜杠菜单项
export const createDefaultSlashMenuItems = (
  imageUploadTrigger?: ImageUploadTrigger,
  modalTrigger?: ModalTrigger,
): SlashMenuItem[] => [
  {
    id: 'heading1',
    name: '标题 1',
    icon: icons.heading1,
    keywords: 'h1 标题 heading',
    shortcut: 'Ctrl+Alt+1',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  {
    id: 'heading2',
    name: '标题 2',
    icon: icons.heading2,
    keywords: 'h2 标题 heading',
    shortcut: 'Ctrl+Alt+2',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  {
    id: 'heading3',
    name: '标题 3',
    icon: icons.heading3,
    keywords: 'h3 标题 heading',
    shortcut: 'Ctrl+Alt+3',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 3 }).run(),
  },
  '|',
  {
    id: 'bold',
    name: '加粗',
    icon: icons.bold,
    keywords: 'bold 加粗 粗体 b',
    shortcut: 'Ctrl+B',
    action: (editor: Editor) => editor.chain().focus().toggleBold().run(),
  },
  {
    id: 'italic',
    name: '斜体',
    icon: icons.italic,
    keywords: 'italic 斜体 i',
    shortcut: 'Ctrl+I',
    action: (editor: Editor) => editor.chain().focus().toggleItalic().run(),
  },
  {
    id: 'strike',
    name: '删除线',
    icon: icons.strike,
    keywords: 'strike 删除线 strikethrough',
    action: (editor: Editor) => editor.chain().focus().toggleStrike().run(),
  },
  {
    id: 'underline',
    name: '下划线',
    icon: icons.underline,
    keywords: 'underline 下划线 u',
    shortcut: 'Ctrl+U',
    action: (editor: Editor) => editor.chain().focus().toggleUnderline().run(),
  },
  {
    id: 'code',
    name: '行内代码',
    icon: icons.code,
    keywords: 'code 代码 行内代码 inline',
    shortcut: 'Ctrl+E',
    action: (editor: Editor) => editor.chain().focus().toggleCode().run(),
  },
  '|',
  {
    id: 'bulletList',
    name: '无序列表',
    icon: icons.bulletList,
    keywords: 'ul 列表 list bullet',
    action: (editor: Editor) => editor.chain().focus().toggleBulletList().run(),
  },
  {
    id: 'orderedList',
    name: '有序列表',
    icon: icons.orderedList,
    keywords: 'ol 列表 list ordered number',
    action: (editor: Editor) => editor.chain().focus().toggleOrderedList().run(),
  },
  {
    id: 'taskList',
    name: '任务列表',
    icon: icons.taskList,
    keywords: 'todo 任务 task checklist',
    action: (editor: Editor) => editor.chain().focus().toggleTaskList().run(),
  },
  '|',
  {
    id: 'blockquote',
    name: '引用',
    icon: icons.blockquote,
    keywords: 'quote 引用 blockquote',
    action: (editor: Editor) => editor.chain().focus().toggleBlockquote().run(),
  },
  {
    id: 'codeBlock',
    name: '代码块',
    icon: icons.codeBlock,
    keywords: 'code 代码 codeblock',
    shortcut: 'Ctrl+Alt+C',
    action: (editor: Editor) => editor.chain().focus().toggleCodeBlock().run(),
  },
  '|',
  {
    id: 'image',
    name: '图片',
    icon: icons.image,
    keywords: 'image 图片 img picture',
    action: (editor: Editor) => {
      // 使用与工具栏一致的图片上传逻辑
      if (imageUploadTrigger) {
        imageUploadTrigger()
      } else {
        // 降级方案：使用简单的URL输入
        const src = prompt('请输入图片URL:')
        if (src) {
          editor.chain().focus().setImage({ src }).run()
        }
      }
    },
  },
  {
    id: 'link',
    name: '链接',
    icon: icons.link,
    keywords: 'link 链接 url',
    action: (editor: Editor) => {
      // 使用与工具栏一致的链接插入逻辑（两个输入框）
      if (modalTrigger) {
        modalTrigger('插入链接', () => {}, false)
      } else {
        // 降级方案：使用简单的URL输入
        const href = prompt('请输入链接URL:')
        if (href) {
          editor.chain().focus().setLink({ href }).run()
        }
      }
    },
  },
  {
    id: 'bilibili',
    name: 'B站视频',
    icon: icons.bilibili,
    keywords: 'bilibili b站 视频 video',
    action: (editor: Editor) => {
      // 使用与工具栏一致的B站视频插入逻辑
      if (modalTrigger) {
        modalTrigger('插入bilibili视频链接', () => {}, true)
      } else {
        // 降级方案：使用简单的URL输入
        const src = prompt('请输入B站视频链接:')
        if (src) {
          // @ts-expect-error - setBilibiliVideo is a custom command that may not be typed
          editor.commands.setBilibiliVideo({ src })
        }
      }
    },
  },
  {
    id: 'horizontalRule',
    name: '分割线',
    icon: icons.horizontalRule,
    keywords: 'hr 分割线 divider line',
    action: (editor: Editor) => editor.chain().focus().setHorizontalRule().run(),
  },
]
